package fileio

import (
	"encoding/binary"
	"encoding/gob"
	"encoding/json"
	"fmt"
	pb "index_poc/protob"
	"io"
	"log"
	"strconv"

	"os"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/xitongsys/parquet-go-source/local"
	"github.com/xitongsys/parquet-go/parquet"
	"github.com/xitongsys/parquet-go/writer"
	"google.golang.org/protobuf/proto"
)

type FileIo struct {
	FileName string
	Ctype    string
}

type Precord struct {
	LineNumber int32  `parquet:"name=linenumber, type=INT32, encoding=PLAIN"`
	Value      string `parquet:"name=value, type=BYTE_ARRAY, convertedtype=UTF8, encoding=PLAIN_DICTIONARY"`
}

func (f *FileIo) DumpMapToFileGetLDB(data map[int32]string) error {

	dirPath := "/Users/<USER>/workspace/indexing_poc/" + f.Ctype + "/" + f.FileName
	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		// Create the directory
		if err := os.Mkdir(dirPath, 0755); err != nil {
			fmt.Println("Error creating directory:", err)
			return err
		}
		fmt.Println("Directory created successfully.")
	}
	db, err := leveldb.OpenFile(dirPath, nil)
	if err != nil {
		log.Fatal(err)
	}
	defer db.Close()
	// var newKey int32
	// Iterate over the map and insert each key-value pair into the database
	for key, value := range data {

		if err := db.Put([]byte(strconv.Itoa(int(key))), []byte(value), nil); err != nil {
			log.Fatal(err)
			return err
		}
		// newKey = key
	}
	// res, _ := db.Get([]byte(strconv.Itoa(int(newKey))), nil)
	// fmt.Println(string(res))
	return nil
}

func (f *FileIo) LoadMapFromFileGetLDB() (*leveldb.DB, error) {
	// Open the file
	db, err := leveldb.OpenFile("/Users/<USER>/workspace/indexing_poc/"+f.Ctype+"/"+f.FileName, nil)
	if err != nil {
		log.Fatal(err)
	}

	return db, nil
}

func (f *FileIo) DumpMapToFileGetProto(data map[int32]string) error {

	mapData := &pb.MapData{ // Use the generated Go struct
		Data: data,
	}
	// Create or truncate the file
	file_path := "/Users/<USER>/workspace/indexing_poc/" + f.Ctype

	file, err := os.Create(file_path + "/" + f.FileName)
	if err != nil {
		return err
	}
	defer file.Close()
	serializedData, err := proto.Marshal(mapData)
	if err != nil {
		log.Fatalf("Error marshalling protocol buffer: %v", err)
	}

	// Write the bytes to the file
	_, err = file.Write(serializedData)
	if err != nil {
		log.Fatalf("Error writing to file: %v", err)
	}

	return nil
}

func (f *FileIo) DumpMapToFileGet(data map[int32]string) error {
	// Create or truncate the file
	file_path := "/Users/<USER>/workspace/indexing_poc/" + f.Ctype

	file, err := os.Create(file_path + "/" + f.FileName)
	if err != nil {
		return err
	}
	defer file.Close()

	// Create a new encoder
	encoder := gob.NewEncoder(file)

	// Encode the data and write it to the file
	err = encoder.Encode(data)
	if err != nil {
		return err
	}

	return nil
}

func (f *FileIo) DumpMapToFileParquet(data map[int32]string) error {
	var err error
	file_path := "/Users/<USER>/workspace/indexing_poc/" + f.Ctype
	fw, err := local.NewLocalFileWriter(file_path + "/" + f.FileName)
	if err != nil {
		log.Println("Can't create local file", err)
		return err
	}

	//write
	pw, err := writer.NewParquetWriter(fw, new(Precord), 4)
	if err != nil {
		log.Println("Can't create parquet writer", err)
		return err
	}

	pw.RowGroupSize = 128 * 1024 * 1024 //128M
	pw.PageSize = 8 * 1024              //8K
	pw.CompressionType = parquet.CompressionCodec_SNAPPY
	for l, d := range data {
		var temp Precord
		temp.LineNumber = l
		temp.Value = d
		if err = pw.Write(temp); err != nil {
			log.Println("Write error", err)
		}
	}
	if err = pw.WriteStop(); err != nil {
		log.Println("WriteStop error", err)
		return err
	}
	log.Println("Write Finished")
	fw.Close()

	return nil
}

type Record struct {
	LineNumber uint32
	VLen       uint32
	Value      string
}

func (f *FileIo) DumpMapToFileGetInx(data []Record) error {
	// Create or truncate the file
	file_path := "/Users/<USER>/workspace/indexing_poc/" + f.Ctype

	file, err := os.Create(file_path + "/" + f.FileName)
	if err != nil {
		return err
	}
	defer file.Close()

	nRecords := len(data)
	var nRecordsBuffer, valueBuffer []byte
	indexBuffer := make([]byte, 0, nRecords*12)
	nRecordsBuffer = make([]byte, 4)

	binary.BigEndian.PutUint32(nRecordsBuffer, uint32(nRecords))
	file.Write(nRecordsBuffer)
	valueOffsetStart := 4 + (nRecords * 12)
	for _, val := range data {
		var lineNumberBuffer, byteOffset, vLenBuffer []byte
		lineNumberBuffer = make([]byte, 4)
		byteOffset = make([]byte, 4)
		vLenBuffer = make([]byte, 4)

		binary.BigEndian.PutUint32(lineNumberBuffer, uint32(val.LineNumber))
		binary.BigEndian.PutUint32(byteOffset, uint32(valueOffsetStart))
		binary.BigEndian.PutUint32(vLenBuffer, uint32(val.VLen))
		valueOffsetStart = valueOffsetStart + int(val.VLen)

		indexBuffer = append(indexBuffer, lineNumberBuffer...)
		// file.Write(lineNumberBuffer)
		// file.Write(byteOffset)
		// file.Write(vLenBuffer)
		indexBuffer = append(indexBuffer, byteOffset...)
		indexBuffer = append(indexBuffer, vLenBuffer...)

		valueBuffer = append(valueBuffer, []byte(val.Value)...)
	}
	file.Write(indexBuffer)
	file.Write(valueBuffer)
	file.Close()

	return nil
}

func (f *FileIo) DumpMapToFileGetEasyJson(data map[int32]string) error {
	// Create or truncate the file
	file_path := "/Users/<USER>/workspace/indexing_poc/" + f.Ctype

	file, err := os.Create(file_path + "/" + f.FileName)
	if err != nil {
		return err
	}
	defer file.Close()

	// Create a new encoder
	encoder := json.NewEncoder(file)

	// Encode the data and write it to the file
	err = encoder.Encode(data)
	if err != nil {
		return err
	}

	return nil
}

func (f *FileIo) DumpMapToFileSearch(data map[string][]int) error {
	// Create or truncate the file
	file_path := "/Users/<USER>/workspace/indexing_poc/" + f.Ctype

	file, err := os.Create(file_path + "/" + f.FileName)
	if err != nil {
		return err
	}
	defer file.Close()

	// Create a new encoder
	encoder := gob.NewEncoder(file)

	// Encode the data and write it to the file

	err = encoder.Encode(data)
	if err != nil {
		fmt.Println(err)
		return err
	}

	return nil
}

type RecordInxValue struct {
	ListStartOffset uint32
	valueOffset     uint32
}

func (f *FileIo) DumpMapToFileSearchEx(data map[string][]int, invertedInxData []Record) error {
	// Create or truncate the file
	file_path := "/Users/<USER>/workspace/indexing_poc/" + f.Ctype
	file, err := os.Create(file_path + "/" + f.FileName)
	if err != nil {
		return err
	}
	defer file.Close()
	// Place holder for inverted index data
	uniqValueslist, uniqueValueTotalBytes := getKeyLenRecord(data)
	// Get total number of bytes required put unique values data
	// in three Blocks
	// <<value Len (4 bytes)>><<Start offset of line number list (4 Bytes)>><<Actul value string>>
	// map hold offset of each individule value
	uniqValueOffsetMap, offset := getValuesOffset(file, uniqValueslist)

	TotalBytesWrittenBuff := make([]byte, int64(offset))
	// Create place holder for Uniq field value and assosiated offset for line number list
	file.Write(TotalBytesWrittenBuff)
	// Dump list of line number field value and line number list
	TotalBytesWritten := dumpIndexData(file, data, uniqValueOffsetMap, offset)

	// Dump unique values and offset of line number list
	file.Seek(8, io.SeekStart)
	dumpUniqueValues(file, uniqValueOffsetMap)
	file.Seek(0, io.SeekStart)
	lastOffset := 8 + TotalBytesWritten + uint32(uniqueValueTotalBytes)
	binary.BigEndian.PutUint32(TotalBytesWrittenBuff[0:4], uint32(lastOffset))
	binary.BigEndian.PutUint32(TotalBytesWrittenBuff[4:8], uint32(uniqueValueTotalBytes+8))
	file.Write(TotalBytesWrittenBuff[0:8])
	file.Seek(int64(lastOffset), io.SeekStart)
	dumpInvertedIndexData(file, invertedInxData, lastOffset, uniqValueOffsetMap)
	return nil

}

func dumpUniqueValues(file *os.File, uniqValueOffsetMap map[string]RecordInxValue) {
	offset := uint32(8)
	for key, val := range uniqValueOffsetMap {
		offsetBuffer := make([]byte, 4)
		valueLenBuffer := make([]byte, 4)
		binary.BigEndian.PutUint32(offsetBuffer, val.ListStartOffset)
		binary.BigEndian.PutUint32(valueLenBuffer, uint32(len(key)))
		file.Write(valueLenBuffer)
		file.Write(offsetBuffer)
		file.Write([]byte(key))
		tempInexValue := uniqValueOffsetMap[key]
		tempInexValue.valueOffset = offset
		uniqValueOffsetMap[key] = tempInexValue
		offset = offset + uint32(8) + uint32(len(key))
	}
}

func getValuesOffset(file *os.File, uniqValueslist []Record) (map[string]RecordInxValue, uint32) {
	uniqValueOffsetMap := make(map[string]RecordInxValue)
	// First two bytes will be the total index data length unique values length
	offset := uint32(8)
	for _, val := range uniqValueslist {
		var recordInxValue RecordInxValue
		recordInxValue.valueOffset = uint32(offset)
		uniqValueOffsetMap[val.Value] = recordInxValue
		offset = offset + uint32(8) + uint32(val.VLen)
	}
	return uniqValueOffsetMap, offset

}

func getKeyLenRecord(data map[string][]int) ([]Record, int) {

	var uniqValueslist []Record
	totalLen := 0

	for key, _ := range data {
		totalLen = totalLen + len(key) + 8 // 4 bytes for key length
		uniqValueslist = append(uniqValueslist, Record{LineNumber: 0, VLen: uint32(len(key)), Value: key})

	}
	return uniqValueslist, totalLen
}

func dumpInvertedIndexData(file *os.File, invertedInxData []Record, lastOffset uint32, uniqValueMap map[string]RecordInxValue) {
	nRecords := len(invertedInxData)
	var nRecordsBuffer []byte
	indexBuffer := make([]byte, 0, nRecords*8)
	nRecordsBuffer = make([]byte, 4)
	//var valueBlockList []RecordInx

	binary.BigEndian.PutUint32(nRecordsBuffer, uint32(nRecords))
	file.Write(nRecordsBuffer)
	//valueOffset := uint32(4 + (nRecords * 8) + int(lastOffset))
	//valueOffset := uint32(0)
	for _, val := range invertedInxData {
		var lineNumberBuffer, byteOffset []byte
		lineNumberBuffer = make([]byte, 4)
		byteOffset = make([]byte, 4)
		// To keep only unique values to reduce file size keep a map of values
		// and only offset will be written to inverted index block
		// if _, ok := mapData[val.Value]; !ok {
		// 	mapData[val.Value] = valueOffset
		// 	binary.BigEndian.PutUint32(vLenBuffer, uint32(val.VLen))
		// 	valueBuffer = append(valueBuffer, vLenBuffer...)
		// 	valueBuffer = append(valueBuffer, []byte(val.Value)...)
		// 	valueOffset = valueOffset + uint32(4) + val.VLen

		// }
		temp := uniqValueMap[val.Value]
		binary.BigEndian.PutUint32(lineNumberBuffer, uint32(val.LineNumber))
		binary.BigEndian.PutUint32(byteOffset, uint32(temp.valueOffset))

		indexBuffer = append(indexBuffer, lineNumberBuffer...)
		// file.Write(lineNumberBuffer)
		// file.Write(byteOffset)
		// file.Write(vLenBuffer)
		indexBuffer = append(indexBuffer, byteOffset...)

	}

	file.Write(indexBuffer)
	file.Close()

}

func dumpIndexData(file *os.File, data map[string][]int, uniqValueMap map[string]RecordInxValue, offset uint32) uint32 {
	var TotalBytesWritten uint32

	offsetListLenbytes := make([]byte, 4)
	offsetListbytes := make([]byte, 4)
	startOffset := offset
	for key, value := range data {
		// keyLen := len(key)
		valueLen := len(value)
		tempValueList := uniqValueMap[key]
		tempValueList.ListStartOffset = startOffset
		uniqValueMap[key] = tempValueList
		// binary.BigEndian.PutUint32(keyLenbytes, uint32(keyLen))
		binary.BigEndian.PutUint32(offsetListLenbytes, uint32(valueLen))
		file.Write(offsetListLenbytes)

		for _, val := range value {

			binary.BigEndian.PutUint32(offsetListbytes, uint32(val))
			file.Write(offsetListbytes)

		}
		startOffset = startOffset + uint32(4+valueLen*4)
		TotalBytesWritten = TotalBytesWritten + uint32(4+valueLen*4)

	}

	return TotalBytesWritten
}

func (f *FileIo) LoadMapFromFileGetProto() (map[int32]string, error) {
	// Open the file
	file_path := "/Users/<USER>/workspace/indexing_poc/" + f.Ctype

	file, err := os.Open(file_path + "/" + f.FileName)
	if err != nil {
		return nil, err
	}

	fileInfo, err := file.Stat()
	if err != nil {
		log.Fatalf("Error getting file info: %v", err)
	}
	fileSize := fileInfo.Size()

	// Read the contents of the file into a byte slice
	serializedData := make([]byte, fileSize)
	_, err = file.Read(serializedData)
	if err != nil {
		log.Fatalf("Error reading file: %v", err)
	}

	// Create an empty MapData struct
	var mapData pb.MapData

	// Unmarshal the serialized data into the MapData struct
	err = proto.Unmarshal(serializedData, &mapData)
	if err != nil {
		log.Fatalf("Error unmarshalling protocol buffer: %v", err)
	}

	// Access the map data
	data := mapData.GetData()
	return data, nil

}

func (f *FileIo) LoadMapFromFileGet() (map[int32]string, error) {
	// Open the file
	file_path := "/Users/<USER>/workspace/indexing_poc/" + f.Ctype

	file, err := os.Open(file_path + "/" + f.FileName)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// Create a new decoder
	decoder := gob.NewDecoder(file)

	// Decode the data into a map
	var data map[int32]string
	err = decoder.Decode(&data)
	if err != nil {

		return nil, err
	}
	return data, nil
}

func (f *FileIo) LoadMapFromFileSearch() (map[string][]int, error) {
	// Open the file
	file_path := "/Users/<USER>/workspace/indexing_poc/" + f.Ctype

	file, err := os.Open(file_path + "/" + f.FileName)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// Create a new decoder
	decoder := gob.NewDecoder(file)

	// Decode the data into a map
	var data map[string][]int
	err = decoder.Decode(&data)
	if err != nil {
		return nil, err
	}
	return data, nil
}
