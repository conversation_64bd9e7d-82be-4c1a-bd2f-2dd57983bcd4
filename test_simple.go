package main

import (
	"fmt"
	"time"
)

func main() {
	// Test just the JSON engine
	engine := &JSONQueryEngine{IndexDir: "./test_indexes"}
	
	fmt.Println("Testing JSON Query Engine...")
	
	start := time.Now()
	results, err := engine.Search("source_username", "odjordjevicrp")
	duration := time.Since(start)
	
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	
	fmt.Printf("Found %d results in %v\n", len(results), duration)
	fmt.Printf("First 10 results: %v\n", results[:min(10, len(results))])
	
	// Test protocol search
	start = time.Now()
	results2, err := engine.Search("protocol", "TCP")
	duration2 := time.Since(start)
	
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	
	fmt.Printf("Protocol TCP: Found %d results in %v\n", len(results2), duration2)
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
