package main

import (
	"fmt"
	"testing"
)

var table = []struct {
	col          string
	searchString string
}{
	{col: "protocol", searchString: "TCP"},                  //40%
	{col: "source_username", searchString: "od<PERSON><PERSON><PERSON><PERSON>"}, //2%
	//{col: "rule_name", searchString: "pretium iaculis justo in hac habitasse platea dictumst etiam faucibus cursus urna ut tellus nulla ut erat id"},
}

// // Benchmark function
// func BenchmarkGetResultNew(b *testing.B) {
// 	for _, v := range table {
// 		b.Run(fmt.Sprintf("input_size_%s", v.col), func(b *testing.B) {
// 			for i := 0; i < b.N; i++ {
// 				getResultNew(v.col, v.searchString)
// 			}

// 		})
// 	}
// }

// func BenchmarkSearchExisting(b *testing.B) {
// 	for _, v := range table {
// 		b.Run(fmt.Sprintf("input_size_%s", v.col), func(b *testing.B) {
// 			for i := 0; i < b.N; i++ {
// 				getOffsetExisting(v.col, v.searchString)
// 			}

// 		})
// 	}
//}

// func BenchmarkSearchNew(b *testing.B) {
// 	for _, v := range table {
// 		b.Run(fmt.Sprintf("input_size_%s", v.col), func(b *testing.B) {
// 			for i := 0; i < b.N; i++ {
// 				getOffsetEx(v.col, v.searchString)
// 			}

// 		})
// 	}
// }

// func BenchmarkGetResult(b *testing.B) {
// 	for _, v := range table {
// 		b.Run(fmt.Sprintf("input_size_%s", v.col), func(b *testing.B) {
// 			for i := 0; i < b.N; i++ {
// 				getResult(v.col, v.searchString)
// 			}

// 		})
// 	}
// }

func BenchmarkGetResultExiting(b *testing.B) {
	for _, v := range table {
		b.Run(fmt.Sprintf("input_size_%s", v.col), func(b *testing.B) {
			for i := 0; i < b.N; i++ {
				getResultExisting(v.col, v.searchString)
			}

		})
	}
}

func BenchmarkGetResultInx(b *testing.B) {
	for _, v := range table {
		b.Run(fmt.Sprintf("input_size_%s", v.col), func(b *testing.B) {
			for i := 0; i < b.N; i++ {
				getResultInx(v.col, v.searchString)
			}

		})
	}
}

// func BenchmarkGetResultParquet(b *testing.B) {
// 	for _, v := range table {
// 		b.Run(fmt.Sprintf("input_size_%s", v.col), func(b *testing.B) {
// 			for i := 0; i < b.N; i++ {
// 				getResultParquet(v.col, v.searchString)
// 			}

// 		})
// 	}
// }

// func My(b *testing.B) {
// 	for _, v := range table {
// 		b.Run(fmt.Sprintf("input_size_%s", v.col), func(b *testing.B) {
// 			for i := 0; i < b.N; i++ {
// 				getResultLDB(v.col, v.searchString)
// 			}

// 		})
// 	}
// }

// var offset = []int{1, 6, 8, 9, 11, 12, 13, 15, 17, 21, 23, 24, 26, 34, 37, 39, 41, 42, 49, 57, 58, 61, 62, 69, 70, 77, 80, 82, 87, 88, 92, 97, 101, 103, 105, 108, 112, 114, 116, 121, 125, 126, 128, 129, 130, 134, 135, 136, 140, 143, 145, 146, 148, 150, 151, 152, 159, 161, 162, 164, 166, 167, 169, 171, 173, 174, 175, 176, 178, 179, 183, 185, 187, 189, 191, 194, 196, 198, 199, 200, 208, 210, 213, 217, 223, 224, 233, 240, 242, 245, 246, 249, 251, 253, 255, 260, 262, 264, 265, 266, 273, 279, 280, 282, 285, 287, 288, 289, 291, 294, 298, 299, 300, 305, 307, 308, 316, 321, 326, 327, 328, 330, 339, 341, 342, 347, 350, 354, 356, 359, 361, 363, 370, 371, 377, 379, 380, 383, 384, 385, 388, 391, 392, 393, 400, 401, 403, 404, 405, 406, 409, 410, 411, 413, 416, 417, 418, 421, 422, 424, 429, 431, 434, 437, 440, 441, 443, 444, 449, 451, 455, 458, 459, 460, 463, 464, 469, 470, 471, 472, 475, 484, 485, 490, 495, 499, 500, 519, 524, 525, 526, 529, 531, 533, 539, 540, 543, 544, 545, 546, 549, 555, 561, 563, 566, 568, 576, 578, 584, 585, 586, 591, 594, 596, 599, 604, 607, 611, 613, 614, 618, 619, 627, 628, 629, 631, 632, 633, 635, 637, 639, 641, 643, 648, 649, 650, 653, 661, 671, 678, 680, 683, 688, 689, 690, 693, 695, 696, 700, 702, 709, 710, 713, 714, 715, 716, 719, 722, 739, 742, 743, 745, 754, 755, 756, 757, 759, 760, 763, 764, 767, 769, 775, 776, 778, 781, 787, 797, 798, 799, 800, 804, 806, 807, 817, 818, 826, 831, 832, 844, 850, 852, 853, 857, 859, 860, 862, 865, 870, 877, 883, 888, 889, 890, 891, 892, 899, 900, 905, 908, 913, 918, 919, 926, 927, 928, 930, 932, 933, 936, 943, 945, 951, 955, 957, 959, 960, 966, 968, 969, 978, 980, 984, 985, 989, 999, 1003, 1004, 1005, 1006, 1009, 1010, 1011, 1014, 1016, 1017, 1021, 1022, 1024, 1029, 1032, 1033, 1036, 1044, 1049, 1053, 1055, 1058, 1059, 1062, 1067, 1072, 1078, 1080, 1086, 1087, 1090, 1092, 1099, 1103, 1106, 1107, 1109, 1112, 1116, 1120, 1123, 1127, 1128, 1129, 1133, 1135, 1138, 1139, 1142, 1143, 1144, 1152, 1157, 1162, 1167, 1168, 1169, 1177, 1180, 1182, 1185, 1186, 1189, 1192, 1193, 1197, 1198, 1199, 1219, 1220, 1221, 1222, 1223, 1227, 1230, 1234, 1235, 1238, 1239, 1241, 1244, 1247, 1249, 1255, 1264, 1265, 1266, 1269, 1271, 1274, 1276, 1278, 1280, 1281, 1283, 1286, 1289, 1291, 1302, 1306, 1308, 1313, 1315, 1317, 1320, 1322, 1323, 1324, 1326, 1327, 1330, 1331, 1333, 1336, 1338, 1342, 1343, 1344, 1345, 1346, 1353, 1354, 1357, 1359, 1362, 1365, 1366, 1367, 1371, 1372, 1374, 1378, 1384, 1396, 1397, 1400, 1402, 1403, 1404, 1405, 1406, 1410, 1412, 1415, 1416, 1418, 1422, 1424, 1434, 1436, 1441, 1442, 1444, 1445, 1455, 1461, 1463, 1465, 1467, 1468, 1469, 1472, 1473, 1477, 1479, 1480, 1484, 1489, 1491, 1497, 1499, 1503, 1505, 1507, 1510, 1513, 1520, 1523, 1526, 1528, 1531, 1532, 1534, 1535, 1538, 1539, 1542, 1546, 1547, 1549, 1551, 1555, 1566, 1573, 1574, 1575, 1578, 1581, 1587, 1588, 1594, 1595, 1596, 1598, 1603, 1604, 1605, 1607, 1610, 1612, 1615, 1622, 1623, 1624, 1629, 1630, 1632, 1633, 1634, 1635, 1639, 1640, 1642, 1643, 1645, 1646, 1647, 1648, 1650, 1657, 1658, 1661, 1662, 1664, 1670, 1671, 1672, 1673, 1674, 1675, 1676, 1678, 1683, 1687, 1688, 1689, 1690, 1692, 1698, 1701, 1702, 1703, 1707, 1709, 1718, 1722, 1724, 1730, 1731, 1733, 1735, 1736, 1738, 1739, 1740, 1750, 1751, 1755, 1758, 1763, 1766, 1769, 1773, 1783, 1784, 1786, 1796, 1799, 1801, 1802, 1805, 1806, 1811, 1817, 1820, 1822, 1826, 1835, 1838, 1840, 1841, 1842, 1843, 1853, 1862, 1867, 1870, 1876, 1879, 1881, 1888, 1890, 1895, 1896, 1897, 1900, 1901, 1904, 1909, 1912, 1913, 1915, 1918, 1919, 1922, 1924, 1927, 1929, 1930, 1931, 1934, 1936, 1944, 1948, 1953, 1955, 1956, 1957, 1959, 1964, 1966, 1972, 1976, 1980, 1981, 1983, 1985, 1986, 1990, 1991, 1994, 1996, 2007, 2008, 2013, 2015, 2016, 2023, 2025, 2026, 2031, 2036, 2037, 2038, 2046, 2047, 2049, 2050, 2051, 2053, 2056, 2059, 2060, 2061, 2066, 2067, 2068, 2069, 2070, 2080, 2081, 2082, 2085, 2086, 2091, 2093, 2094, 2095, 2096, 2097, 2100, 2103, 2105, 2106, 2108, 2109, 2117, 2119, 2121, 2123, 2124, 2127, 2131, 2132, 2135, 2137, 2145, 2150, 2151, 2160, 2169, 2170, 2174, 2177, 2181, 2189, 2197, 2198, 2199, 2203, 2209, 2213, 2214, 2217, 2224, 2225, 2226, 2236, 2242, 2243, 2250, 2253, 2257, 2263, 2265, 2266, 2269, 2272, 2288, 2289, 2290, 2293, 2294, 2295, 2297, 2299, 2300, 2303, 2305, 2309, 2310, 2311, 2314, 2316, 2321, 2322, 2329, 2336, 2339, 2340, 2341, 2348, 2353, 2354, 2356, 2358, 2360, 2363, 2365, 2368, 2370, 2372, 2375, 2380, 2382, 2383, 2387, 2388, 2393, 2395, 2403, 2404, 2406, 2408, 2409, 2410, 2411, 2419, 2420, 2421, 2428, 2430, 2431, 2435, 2436, 2438, 2439, 2440, 2444, 2450, 2453, 2455, 2459, 2463, 2466, 2470, 2471, 2473, 2477, 2478, 2479, 2480, 2484, 2491, 2492, 2494, 2495, 2498, 2499, 2505, 2506, 2507, 2510, 2515, 2519, 2523, 2524, 2525, 2534, 2535, 2536, 2538, 2541, 2544, 2545, 2553, 2565, 2566, 2567, 2569, 2570, 2573, 2578, 2580, 2583, 2588, 2589, 2590, 2594, 2596, 2598, 2600, 2603, 2604, 2606, 2607, 2611, 2612, 2614, 2616, 2618, 2623, 2626, 2628, 2632, 2638, 2639, 2644, 2647, 2648, 2654, 2657, 2658, 2663, 2665, 2666, 2672, 2674, 2677, 2680, 2682, 2687, 2688, 2689, 2692, 2693, 2696, 2700, 2706, 2712, 2715, 2716, 2718, 2719, 2720, 2721, 2722, 2724, 2731, 2732, 2734, 2737, 2739, 2741, 2742, 2744, 2747, 2748, 2753, 2759, 2762, 2768, 2769, 2770, 2771, 2772, 2778, 2780, 2782, 2783, 2784, 2786, 2787, 2788, 2789, 2792, 2795, 2799, 2804, 2805, 2806, 2809, 2812, 2817, 2818, 2824, 2825, 2826, 2828, 2835, 2837, 2842, 2845, 2847, 2850, 2859, 2861, 2862, 2865, 2868, 2870, 2871, 2881, 2887, 2888, 2895, 2903, 2905, 2909, 2910, 2919, 2920, 2922, 2927, 2929, 2935, 2939, 2944, 2946, 2948, 2950, 2952, 2958, 2964, 2969, 2977, 2978, 2983, 2984, 2988, 2990, 2991, 2993, 2995, 2999}

// func BenchmarkGetdataLDB(b *testing.B) {
// 	for i := 0; i < b.N; i++ {
// 		getDataLDB(offset, "protocol", "tcp")
// 	}

// }

// func BenchmarkGetdata(b *testing.B) {
// 	for i := 0; i < b.N; i++ {
// 		getData(offset, "protocol", "tcp")
// 	}

// }
