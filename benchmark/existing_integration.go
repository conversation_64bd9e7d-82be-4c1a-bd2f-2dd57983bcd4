package main

import (
	"encoding/binary"
	"fmt"
	"io"
	"os"
	"strings"
	"time"
)

// ExistingQueryEngine integrates with the current implementation
type ExistingQueryEngine struct {
	DataDir string
}

func (e *ExistingQueryEngine) Search(columnName, searchValue string) ([]uint32, error) {
	start := time.Now()

	// Use the existing getResultExisting approach
	offsets := e.getOffsetExisting(columnName, searchValue)

	// Convert int32 to uint32 for consistency
	results := make([]uint32, len(offsets))
	for i, offset := range offsets {
		results[i] = uint32(offset)
	}

	duration := time.Since(start)
	fmt.Printf("  Existing approach found %d results in %v\n", len(results), duration)

	return results, nil
}

func (e *ExistingQueryEngine) GetName() string {
	return "Existing"
}

func (e *ExistingQueryEngine) GetDescription() string {
	return "Current implementation (binary format with sequential scan)"
}

// Adapted from the existing getOffsetExisting function
func (e *ExistingQueryEngine) getOffsetExisting(col, searchString string) []int32 {
	var offsets []int32
	file, err := os.Open(e.DataDir + "/existing/" + col + "_ex.txt")
	if err != nil {
		fmt.Printf("Error opening existing file %s: %v\n", col, err)
		return offsets
	}
	defer file.Close()

	buffer := make([]byte, 8192)
	_, err = file.Read(buffer)
	if err != nil {
		fmt.Println("file end")
		return offsets
	}

	lineAndLen := 4
	start := 0
	var lineNumber, valueLen, valueBuffer []byte

	for {
		start, lineNumber, buffer = e.getBytes(start, lineAndLen, buffer, file)
		if start == 0 {
			break
		}
		start, valueLen, buffer = e.getBytes(start, lineAndLen, buffer, file)
		if start == 0 {
			break
		}

		line := int32(binary.BigEndian.Uint32(lineNumber))
		vLen := int32(binary.BigEndian.Uint32(valueLen))
		if int(vLen) == 0 {
			break
		}

		start, valueBuffer, buffer = e.getBytes(start, int(vLen), buffer, file)
		if start == 0 {
			break
		}

		if strings.EqualFold(searchString, string(valueBuffer)) {
			offsets = append(offsets, line)
		}
	}
	return offsets
}

// Adapted from the existing getBytes function
func (e *ExistingQueryEngine) getBytes(start, bytesToRead int, buffer []byte, file *os.File) (int, []byte, []byte) {
	var byteData []byte
	var end int

	if start >= len(buffer) {
		start = start - len(buffer)
		file.Seek(int64(start), io.SeekCurrent)
		start = 0
		end = start + bytesToRead
		newBuffer := make([]byte, 8192)
		_, err := file.Read(newBuffer)
		if err != nil {
			return 0, byteData, buffer
		}
		byteData = append(byteData, newBuffer[start:end]...)
		buffer = newBuffer
		return end, byteData, buffer
	}

	end = start + bytesToRead
	if end > len(buffer) {
		byteData = buffer[start:]
		end = end - len(buffer)
		for {
			if end > 8192 {
				start = 0
				newBuffer := make([]byte, 8192)
				_, err := file.Read(newBuffer)
				if err != nil {
					return 0, byteData, buffer
				}
				byteData = append(byteData, newBuffer...)
				end = end - len(buffer)
			} else {
				break
			}
		}
		start = 0
		newBuffer := make([]byte, 8192)
		_, err := file.Read(newBuffer)
		if err != nil {
			return 0, byteData, buffer
		}
		byteData = append(byteData, newBuffer[start:end]...)
		buffer = newBuffer
		return end, byteData, buffer
	}

	byteData = buffer[start:end]
	return end, byteData, buffer
}

// ExistingInxQueryEngine integrates with the current index implementation
type ExistingInxQueryEngine struct {
	DataDir string
}

func (e *ExistingInxQueryEngine) Search(columnName, searchValue string) ([]uint32, error) {
	start := time.Now()

	// Use the existing getResultInx approach
	offsetBytes := e.getOffsetInx(columnName, searchValue)

	// Convert byte array to uint32 array
	var results []uint32
	for i := 0; i < len(offsetBytes); i += 4 {
		if i+4 <= len(offsetBytes) {
			offset := binary.BigEndian.Uint32(offsetBytes[i : i+4])
			results = append(results, offset)
		}
	}

	duration := time.Since(start)
	fmt.Printf("  Existing index approach found %d results in %v\n", len(results), duration)

	return results, nil
}

func (e *ExistingInxQueryEngine) GetName() string {
	return "ExistingInx"
}

func (e *ExistingInxQueryEngine) GetDescription() string {
	return "Current index implementation (compressed index with binary search)"
}

// Adapted from the existing getOffsetInx function
func (e *ExistingInxQueryEngine) getOffsetInx(col, searchString string) []byte {
	var offsets []byte
	file, err := os.Open(e.DataDir + "/search/" + col + "_inx_com.txt")
	if err != nil {
		fmt.Printf("Error opening existing index file %s: %v\n", col, err)
		return offsets
	}
	defer file.Close()

	startByteOffsetBuff := make([]byte, 8)
	_, err = file.Read(startByteOffsetBuff)
	if err != nil {
		fmt.Println("file end")
		return offsets
	}

	startoffsetIndexBlock := binary.BigEndian.Uint32((startByteOffsetBuff[4:8]))
	uniqValueListBlockSize := startoffsetIndexBlock - 8

	LineListOffset := e.getListOffset(searchString, file, int(uniqValueListBlockSize))
	if LineListOffset == 0 {
		return offsets
	}

	file.Seek(int64(LineListOffset), io.SeekStart)
	buffer := make([]byte, 4)
	_, err = file.Read(buffer)
	if err != nil {
		return offsets
	}

	bytesToRead := binary.BigEndian.Uint32(buffer) * 4
	buffer = make([]byte, bytesToRead)
	_, err = file.Read(buffer)
	if err != nil {
		return offsets
	}

	return buffer
}

// Adapted from the existing getListOffset function
func (e *ExistingInxQueryEngine) getListOffset(searchString string, file *os.File, bytesToRead int) uint32 {
	serachStringLen := len(searchString)
	bufferSize := 8192

	if bytesToRead > bufferSize {
		bufferSize = bytesToRead
	}

	buffer := make([]byte, bufferSize)
	_, err := file.Read(buffer)
	if err != nil {
		return 0
	}

	for i := 0; i < len(buffer); {
		vLen := binary.BigEndian.Uint32(buffer[i : i+4])
		if vLen == uint32(serachStringLen) {
			listOffset := binary.BigEndian.Uint32(buffer[i+4 : i+8])
			value := buffer[i+8 : i+8+int(vLen)]
			if string(value) == searchString {
				return listOffset
			}
		}
		i += 8 + int(vLen)
	}

	return 0
}

// Enhanced comparison function
func RunComparisonWithExisting(indexDir string, queries []QueryTest) {
	fmt.Printf("\n=== COMPREHENSIVE COMPARISON ===\n")

	// All engines including existing ones
	engines := []QueryEngine{
		// New optimized engines (working ones)
		&UltraFastQueryEngine{IndexDir: indexDir},
		&JSONQueryEngine{IndexDir: indexDir},
		&CompressedQueryEngine{IndexDir: indexDir},
		&LZ4QueryEngine{IndexDir: indexDir},

		// Existing engines for comparison
		&ExistingQueryEngine{DataDir: "../"},
		&ExistingInxQueryEngine{DataDir: "../"},
	}

	results := make([]BenchmarkResult, 0)

	fmt.Printf("%-15s %-30s %-12s %-8s %-10s\n",
		"Engine", "Description", "Avg Time", "Results", "Status")
	fmt.Println(strings.Repeat("-", 85))

	for _, engine := range engines {
		fmt.Printf("\n--- %s ---\n", engine.GetName())

		var totalTime time.Duration
		successCount := 0

		for _, query := range queries {
			start := time.Now()
			lineNums, err := engine.Search(query.Column, query.Value)
			duration := time.Since(start)

			result := BenchmarkResult{
				Engine:      engine.GetName(),
				Column:      query.Column,
				SearchValue: query.Value,
				Duration:    duration,
				ResultCount: len(lineNums),
				Success:     err == nil,
			}

			if err != nil {
				result.Error = err.Error()
				fmt.Printf("  %s: ERROR - %v\n", query.Column, err)
			} else {
				fmt.Printf("  %s: Found %d results in %v\n",
					query.Column, len(lineNums), duration)
				totalTime += duration
				successCount++
			}

			results = append(results, result)
		}

		avgTime := time.Duration(0)
		if successCount > 0 {
			avgTime = totalTime / time.Duration(successCount)
		}

		fmt.Printf("%-15s %-30s %-12v %-8s %-10s\n",
			engine.GetName(), engine.GetDescription(), avgTime,
			fmt.Sprintf("%d/%d", successCount, len(queries)),
			fmt.Sprintf("%.1f%%", float64(successCount)/float64(len(queries))*100))
	}

	// Generate detailed comparison
	generateDetailedComparison(results)

	// Save comprehensive results
	saveResults(results, "comprehensive_benchmark_results.csv")
}

func generateDetailedComparison(results []BenchmarkResult) {
	fmt.Printf("\n=== DETAILED PERFORMANCE COMPARISON ===\n")

	engineStats := make(map[string]struct {
		times       []time.Duration
		successRate float64
		avgResults  float64
	})

	for _, result := range results {
		stats := engineStats[result.Engine]
		if result.Success {
			stats.times = append(stats.times, result.Duration)
			stats.avgResults += float64(result.ResultCount)
		}
		engineStats[result.Engine] = stats
	}

	// Calculate success rates and averages
	queryCount := len(results) / len(engineStats)
	for engine, stats := range engineStats {
		stats.successRate = float64(len(stats.times)) / float64(queryCount) * 100
		if len(stats.times) > 0 {
			stats.avgResults /= float64(len(stats.times))
		}
		engineStats[engine] = stats
	}

	fmt.Printf("%-15s %-12s %-12s %-12s %-12s %-12s\n",
		"Engine", "Avg Time", "Min Time", "Max Time", "Success%", "Avg Results")
	fmt.Println(strings.Repeat("-", 85))

	for engine, stats := range engineStats {
		if len(stats.times) == 0 {
			fmt.Printf("%-15s %-12s %-12s %-12s %-11.1f%% %-12.1f\n",
				engine, "N/A", "N/A", "N/A", stats.successRate, stats.avgResults)
			continue
		}

		var total, min, max time.Duration
		min = time.Hour

		for _, d := range stats.times {
			total += d
			if d < min {
				min = d
			}
			if d > max {
				max = d
			}
		}

		avg := total / time.Duration(len(stats.times))

		fmt.Printf("%-15s %-12v %-12v %-12v %-11.1f%% %-12.1f\n",
			engine, avg, min, max, stats.successRate, stats.avgResults)
	}
}
