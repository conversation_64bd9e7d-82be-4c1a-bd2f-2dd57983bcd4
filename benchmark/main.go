package main

import (
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// BenchmarkResult stores the results of a benchmark run
type BenchmarkResult struct {
	Engine      string
	Column      string
	SearchValue string
	Duration    time.Duration
	ResultCount int
	Success     bool
	Error       string
}

// BenchmarkSuite manages the entire benchmarking process
type BenchmarkSuite struct {
	DataFile    string
	IndexDir    string
	ResultsFile string
	Queries     []QueryTest
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run . <command> [args...]")
		fmt.Println("Commands:")
		fmt.Println("  generate <csv_file> <output_dir>  - Generate optimized indexes")
		fmt.Println("  benchmark <index_dir> <queries_file> - Run benchmark tests")
		fmt.Println("  compare <index_dir> <queries_file> - Compare with existing approach")
		return
	}

	command := os.Args[1]

	switch command {
	case "generate":
		if len(os.Args) < 4 {
			fmt.Println("Usage: go run . generate <csv_file> <output_dir>")
			return
		}
		csvFile := os.Args[2]
		outputDir := os.Args[3]

		fmt.Printf("Generating optimized indexes from %s to %s\n", csvFile, outputDir)
		err := GenerateOptimizedIndexes(csvFile, outputDir)
		if err != nil {
			fmt.Printf("Error generating indexes: %v\n", err)
			return
		}
		fmt.Println("Index generation completed successfully!")

	case "benchmark":
		if len(os.Args) < 4 {
			fmt.Println("Usage: go run . benchmark <index_dir> <queries_file>")
			return
		}
		indexDir := os.Args[2]
		queriesFile := os.Args[3]

		queries, err := loadQueries(queriesFile)
		if err != nil {
			fmt.Printf("Error loading queries: %v\n", err)
			return
		}

		fmt.Printf("Running benchmark with %d queries\n", len(queries))
		runBenchmark(indexDir, queries)

	case "compare":
		if len(os.Args) < 4 {
			fmt.Println("Usage: go run . compare <index_dir> <queries_file>")
			return
		}
		indexDir := os.Args[2]
		queriesFile := os.Args[3]

		queries, err := loadQueries(queriesFile)
		if err != nil {
			fmt.Printf("Error loading queries: %v\n", err)
			return
		}

		fmt.Printf("Comparing approaches with %d queries\n", len(queries))
		runComparison(indexDir, queries)

	default:
		fmt.Printf("Unknown command: %s\n", command)
	}
}

func loadQueries(filename string) ([]QueryTest, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	reader.Comma = '='

	records, err := reader.ReadAll()
	if err != nil {
		return nil, err
	}

	var queries []QueryTest
	for _, record := range records {
		if len(record) >= 2 {
			queries = append(queries, QueryTest{
				Column: record[0],
				Value:  record[1],
			})
		}
	}

	return queries, nil
}

func runBenchmark(indexDir string, queries []QueryTest) {
	engines := []QueryEngine{
		&BinarySearchEngine{IndexDir: indexDir},
		&HashQueryEngine{IndexDir: indexDir},
		&CompressedQueryEngine{IndexDir: indexDir},
		&JSONQueryEngine{IndexDir: indexDir},
		&ColumnarQueryEngine{IndexDir: indexDir},
		&LZ4QueryEngine{IndexDir: indexDir},
	}

	results := make([]BenchmarkResult, 0)

	fmt.Printf("\n=== BENCHMARK RESULTS ===\n")
	fmt.Printf("%-15s %-20s %-15s %-12s %-8s %-10s\n",
		"Engine", "Column", "Search Value", "Duration", "Results", "Status")
	fmt.Println(strings.Repeat("-", 90))

	for _, engine := range engines {
		fmt.Printf("\n--- %s ---\n", engine.GetName())

		for _, query := range queries {
			start := time.Now()
			lineNums, err := engine.Search(query.Column, query.Value)
			duration := time.Since(start)

			result := BenchmarkResult{
				Engine:      engine.GetName(),
				Column:      query.Column,
				SearchValue: query.Value,
				Duration:    duration,
				ResultCount: len(lineNums),
				Success:     err == nil,
			}

			if err != nil {
				result.Error = err.Error()
				fmt.Printf("%-15s %-20s %-15s %-12s %-8s %-10s\n",
					engine.GetName(), query.Column, query.Value,
					duration, "0", "ERROR")
			} else {
				fmt.Printf("%-15s %-20s %-15s %-12s %-8d %-10s\n",
					engine.GetName(), query.Column, query.Value,
					duration, len(lineNums), "SUCCESS")
			}

			results = append(results, result)
		}
	}

	// Generate summary statistics
	generateSummary(results)

	// Save results to file
	saveResults(results, "benchmark_results.csv")
}

func runComparison(indexDir string, queries []QueryTest) {
	fmt.Printf("\n=== COMPARISON: NEW vs EXISTING APPROACHES ===\n")

	// Run comprehensive comparison including existing approaches
	RunComparisonWithExisting(indexDir, queries)
}

func generateSummary(results []BenchmarkResult) {
	fmt.Printf("\n=== SUMMARY STATISTICS ===\n")

	engineStats := make(map[string][]time.Duration)

	for _, result := range results {
		if result.Success {
			engineStats[result.Engine] = append(engineStats[result.Engine], result.Duration)
		}
	}

	fmt.Printf("%-15s %-12s %-12s %-12s %-12s\n",
		"Engine", "Avg Time", "Min Time", "Max Time", "Success Rate")
	fmt.Println(strings.Repeat("-", 70))

	for engine, durations := range engineStats {
		if len(durations) == 0 {
			continue
		}

		var total, min, max time.Duration
		min = time.Hour

		for _, d := range durations {
			total += d
			if d < min {
				min = d
			}
			if d > max {
				max = d
			}
		}

		avg := total / time.Duration(len(durations))
		successRate := float64(len(durations)) / float64(len(results)/len(engineStats)) * 100

		fmt.Printf("%-15s %-12v %-12v %-12v %-11.1f%%\n",
			engine, avg, min, max, successRate)
	}
}

func saveResults(results []BenchmarkResult, filename string) {
	file, err := os.Create(filename)
	if err != nil {
		fmt.Printf("Error creating results file: %v\n", err)
		return
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// Write header
	writer.Write([]string{"Engine", "Column", "SearchValue", "Duration", "ResultCount", "Success", "Error"})

	// Write results
	for _, result := range results {
		record := []string{
			result.Engine,
			result.Column,
			result.SearchValue,
			result.Duration.String(),
			fmt.Sprintf("%d", result.ResultCount),
			fmt.Sprintf("%t", result.Success),
			result.Error,
		}
		writer.Write(record)
	}

	fmt.Printf("\nResults saved to %s\n", filename)
}

// File size analysis
func analyzeFileSizes(indexDir string) {
	fmt.Printf("\n=== FILE SIZE ANALYSIS ===\n")
	fmt.Printf("%-30s %-15s %-15s\n", "File", "Size (bytes)", "Size (MB)")
	fmt.Println(strings.Repeat("-", 65))

	err := filepath.Walk(indexDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() {
			sizeMB := float64(info.Size()) / (1024 * 1024)
			fmt.Printf("%-30s %-15d %-15.2f\n",
				filepath.Base(path), info.Size(), sizeMB)
		}
		return nil
	})

	if err != nil {
		fmt.Printf("Error analyzing file sizes: %v\n", err)
	}
}
