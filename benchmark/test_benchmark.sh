#!/bin/bash

# Test script for the indexing benchmark suite

echo "=== Indexing Benchmark Suite Test ==="
echo

# Check if required files exist
if [ ! -f "../mock_data.csv" ]; then
    echo "Error: mock_data.csv not found in parent directory"
    exit 1
fi

if [ ! -f "../search_string.txt" ]; then
    echo "Error: search_string.txt not found in parent directory"
    exit 1
fi

# Create output directory
mkdir -p ./test_indexes

echo "1. Generating optimized indexes..."
go run . generate ../mock_data.csv ./test_indexes

if [ $? -eq 0 ]; then
    echo "✓ Index generation completed successfully"
else
    echo "✗ Index generation failed"
    exit 1
fi

echo
echo "2. Analyzing generated file sizes..."
echo "File sizes in test_indexes directory:"
ls -lh ./test_indexes/ | grep -v "^total"

echo
echo "3. Running benchmark tests..."
go run . benchmark ./test_indexes ../search_string.txt

if [ $? -eq 0 ]; then
    echo "✓ Benchmark completed successfully"
else
    echo "✗ Benchmark failed"
    exit 1
fi

echo
echo "4. Results saved to benchmark_results.csv"
if [ -f "benchmark_results.csv" ]; then
    echo "✓ Results file created"
    echo "First few lines of results:"
    head -5 benchmark_results.csv
else
    echo "✗ Results file not found"
fi

echo
echo "=== Test completed successfully! ==="
echo
echo "Next steps:"
echo "- Review benchmark_results.csv for detailed performance data"
echo "- Compare file sizes in ./test_indexes/ directory"
echo "- Run 'go run . compare ./test_indexes ../search_string.txt' for comparison"
echo "- Modify search_string.txt to test different queries"
