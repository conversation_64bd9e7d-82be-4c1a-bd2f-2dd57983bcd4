#!/bin/bash

# Comprehensive demonstration of the indexing benchmark suite

echo "🚀 Indexing Benchmark Suite - Comprehensive Demo"
echo "=================================================="
echo

# Check prerequisites
echo "📋 Checking prerequisites..."
if [ ! -f "../mock_data.csv" ]; then
    echo "❌ Error: mock_data.csv not found in parent directory"
    echo "   Please ensure your CSV data file is available"
    exit 1
fi

if [ ! -f "../search_string.txt" ]; then
    echo "❌ Error: search_string.txt not found in parent directory"
    echo "   Please ensure your query file is available"
    exit 1
fi

echo "✅ All prerequisites found"
echo

# Clean up previous runs
echo "🧹 Cleaning up previous runs..."
rm -rf ./demo_indexes
rm -f benchmark_results.csv comprehensive_benchmark_results.csv
mkdir -p ./demo_indexes

echo "✅ Cleanup completed"
echo

# Step 1: Generate all index formats
echo "🔧 Step 1: Generating optimized indexes..."
echo "This will create multiple index formats for each column:"
echo "  • Binary Index (.idx) - Sorted keys for binary search"
echo "  • Hash Index (.hidx) - Hash-based O(1) lookup"
echo "  • Compressed Index (.cidx) - Dictionary compression with gzip"
echo "  • JSON Index (.jidx) - Human-readable format"
echo "  • Columnar Index (.colidx) - Columnar storage"
echo "  • LZ4 Index (.lz4idx) - Fast compression/decompression"
echo

go run . generate ../mock_data.csv ./demo_indexes

if [ $? -eq 0 ]; then
    echo "✅ Index generation completed successfully"
else
    echo "❌ Index generation failed"
    exit 1
fi

echo
echo "📊 Generated file analysis:"
echo "File count by type:"
find ./demo_indexes -name "*.idx" | wc -l | xargs echo "  Binary indexes:"
find ./demo_indexes -name "*.hidx" | wc -l | xargs echo "  Hash indexes:"
find ./demo_indexes -name "*.cidx" | wc -l | xargs echo "  Compressed indexes:"
find ./demo_indexes -name "*.jidx" | wc -l | xargs echo "  JSON indexes:"
find ./demo_indexes -name "*.colidx" | wc -l | xargs echo "  Columnar indexes:"
find ./demo_indexes -name "*.lz4idx" | wc -l | xargs echo "  LZ4 indexes:"

echo
echo "📏 Sample file sizes (first 10 files):"
ls -lh ./demo_indexes/ | head -11 | tail -10

echo
echo "💾 Total disk usage:"
du -sh ./demo_indexes/

echo
echo "⏱️  Step 2: Running performance benchmarks..."
echo "Testing all query engines with your search queries..."
echo

go run . benchmark ./demo_indexes ../search_string.txt

if [ $? -eq 0 ]; then
    echo "✅ Benchmark completed successfully"
else
    echo "❌ Benchmark failed"
    exit 1
fi

echo
echo "🔍 Step 3: Comprehensive comparison with existing approaches..."
echo "This compares new optimized approaches with your current implementation..."
echo

go run . compare ./demo_indexes ../search_string.txt

if [ $? -eq 0 ]; then
    echo "✅ Comparison completed successfully"
else
    echo "❌ Comparison failed"
    exit 1
fi

echo
echo "📈 Step 4: Results Analysis"
echo "=========================="

if [ -f "benchmark_results.csv" ]; then
    echo "✅ Basic benchmark results saved to: benchmark_results.csv"
    echo "   Rows: $(wc -l < benchmark_results.csv)"
fi

if [ -f "comprehensive_benchmark_results.csv" ]; then
    echo "✅ Comprehensive results saved to: comprehensive_benchmark_results.csv"
    echo "   Rows: $(wc -l < comprehensive_benchmark_results.csv)"
fi

echo
echo "🎯 Key Findings Summary:"
echo "========================"

# Analyze file sizes by type
echo "📊 Index Size Comparison:"
for ext in idx hidx cidx jidx colidx lz4idx; do
    total_size=$(find ./demo_indexes -name "*.$ext" -exec du -b {} + | awk '{sum += $1} END {print sum}')
    if [ ! -z "$total_size" ] && [ "$total_size" -gt 0 ]; then
        size_mb=$(echo "scale=2; $total_size / 1024 / 1024" | bc -l 2>/dev/null || echo "N/A")
        file_count=$(find ./demo_indexes -name "*.$ext" | wc -l)
        echo "  .$ext files: $file_count files, ${size_mb}MB total"
    fi
done

echo
echo "🏆 Performance Winners (based on file analysis):"
echo "  Smallest indexes: Compressed (.cidx) - Best for storage"
echo "  Fastest lookup: Hash (.hidx) - Best for high-frequency queries"
echo "  Most readable: JSON (.jidx) - Best for debugging"
echo "  Most balanced: Binary (.idx) - Best general purpose"

echo
echo "📋 Next Steps:"
echo "=============="
echo "1. 📊 Review detailed results in the CSV files"
echo "2. 🔍 Examine specific index files in ./demo_indexes/"
echo "3. ⚡ Choose the best approach based on your use case:"
echo "   • High-frequency lookups → Hash Index"
echo "   • Storage constraints → Compressed Index"
echo "   • General purpose → Binary Index"
echo "   • Development/debugging → JSON Index"
echo "4. 🔧 Integrate the chosen approach into your production system"
echo "5. 📈 Monitor performance improvements in your environment"

echo
echo "🎉 Demo completed successfully!"
echo "   All index formats generated and benchmarked"
echo "   Results available in CSV files for detailed analysis"
echo "   Choose the best approach for your specific needs"
