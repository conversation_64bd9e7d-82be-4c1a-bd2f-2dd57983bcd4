# Indexing Benchmark Suite

This benchmark suite provides optimized file generation and query functions to compare different search approaches with various file formats and indexing strategies.

## Features

### File Generators
1. **Binary Index Generator** (.idx) - Sorted keys for binary search, O(log n) lookup
2. **Hash Index Generator** (.hidx) - Hash-based index for O(1) average lookup
3. **Compressed Index Generator** (.cidx) - Dictionary compression with gzip
4. **JSON Index Generator** (.jidx) - Human-readable format for debugging
5. **Columnar Index Generator** (.colidx) - Columnar storage for analytical workloads
6. **LZ4 Index Generator** (.lz4idx) - Fast compression/decompression

### Query Engines
- **BinarySearch** - Binary search on sorted index
- **HashLookup** - Hash-based O(1) lookup
- **Compressed** - Dictionary-compressed index queries
- **JSON** - JSON-based index lookup
- **Columnar** - Columnar storage scan
- **LZ4** - LZ4-compressed index lookup

## Usage

### 1. Generate Optimized Indexes

```bash
cd benchmark
go run . generate ../mock_data.csv ./indexes
```

This will create multiple index files for each column in different formats:
- `column_binary.idx` - Binary searchable index
- `column_hash.hidx` - Hash-based index
- `column_compressed.cidx` - Compressed index
- `column_json.jidx` - JSON index
- `column_columnar.colidx` - Columnar index
- `column_lz4.lz4idx` - LZ4 compressed index

### 2. Run Benchmarks

```bash
go run . benchmark ./indexes ../search_string.txt
```

This will:
- Test all query engines against your search queries
- Measure execution time for each approach
- Generate detailed performance statistics
- Save results to `benchmark_results.csv`

### 3. Compare with Existing Approach

```bash
go run . compare ./indexes ../search_string.txt
```

This will run both new optimized approaches and provide comparison metrics.

## Query File Format

The query file should be in CSV format with `=` as delimiter:
```
column_name=search_value
source_username=odjordjevicrp
protocol=TCP
```

## Output

### Console Output
```
=== BENCHMARK RESULTS ===
Engine          Column               Search Value    Duration     Results  Status    
------------------------------------------------------------------------------------------
BinarySearch    source_username      odjordjevicrp   245.2µs      5        SUCCESS   
HashLookup      source_username      odjordjevicrp   123.1µs      5        SUCCESS   
Compressed      source_username      odjordjevicrp   456.7µs      5        SUCCESS   
JSON            source_username      odjordjevicrp   189.3µs      5        SUCCESS   
```

### Summary Statistics
```
=== SUMMARY STATISTICS ===
Engine          Avg Time     Min Time     Max Time     Success Rate
----------------------------------------------------------------------
BinarySearch    245.2µs      123.1µs      567.8µs      100.0%      
HashLookup      123.1µs      89.2µs       234.5µs      100.0%      
Compressed      456.7µs      234.1µs      789.3µs      100.0%      
JSON            189.3µs      145.6µs      298.7µs      100.0%      
```

### File Size Analysis
```
=== FILE SIZE ANALYSIS ===
File                           Size (bytes)    Size (MB)      
---------------------------------------------------------------
source_username_binary.idx     1024567        0.98           
source_username_hash.hidx       2048123        1.95           
source_username_compressed.cidx 512345         0.49           
source_username_json.jidx       1536789        1.47           
```

## Performance Characteristics

| Approach | Lookup Time | Index Size | Memory Usage | Best For |
|----------|-------------|------------|--------------|----------|
| Binary Search | O(log n) | Medium | Low | General purpose |
| Hash Lookup | O(1) avg | Large | Medium | High-frequency lookups |
| Compressed | O(n) | Small | Low | Storage-constrained |
| JSON | O(1) | Large | High | Development/debugging |
| Columnar | O(n) | Medium | Medium | Analytical queries |
| LZ4 | O(1) | Small | Medium | Fast decompression |

## Extending the Framework

### Adding New Generators

1. Implement the `IndexGenerator` interface:
```go
type MyGenerator struct {
    OutputDir string
}

func (g *MyGenerator) Generate(columnName string, data []IndexRecord) error {
    // Your implementation
}

func (g *MyGenerator) GetFileExtension() string {
    return ".myext"
}

func (g *MyGenerator) GetDescription() string {
    return "My custom index format"
}
```

2. Add to the generators list in `GenerateOptimizedIndexes()`

### Adding New Query Engines

1. Implement the `QueryEngine` interface:
```go
type MyQueryEngine struct {
    IndexDir string
}

func (e *MyQueryEngine) Search(columnName, searchValue string) ([]uint32, error) {
    // Your implementation
}

func (e *MyQueryEngine) GetName() string {
    return "MyEngine"
}

func (e *MyQueryEngine) GetDescription() string {
    return "My custom query engine"
}
```

2. Add to the engines list in `runBenchmark()`

## Dependencies

- Go 1.21+
- Standard library packages
- Optional: github.com/pierrec/lz4/v4 for real LZ4 compression

## Files Generated

The benchmark suite generates various file types with different extensions:
- `.idx` - Binary index files
- `.hidx` - Hash index files  
- `.cidx` - Compressed index files
- `.jidx` - JSON index files
- `.colidx` - Columnar index files
- `.lz4idx` - LZ4 compressed index files
- `.csv` - Benchmark results

All files are optimized for their specific use cases and can be used independently or compared against each other.
