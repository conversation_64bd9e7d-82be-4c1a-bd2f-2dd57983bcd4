package main

import (
	"compress/gzip"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"
)

// Query engine interface
type QueryEngine interface {
	Search(columnName, searchValue string) ([]uint32, error)
	GetName() string
	GetDescription() string
}

// 1. Binary Search Query Engine
type BinarySearchEngine struct {
	IndexDir string
}

func (e *BinarySearchEngine) Search(columnName, searchValue string) ([]uint32, error) {
	filename := fmt.Sprintf("%s/%s_binary.idx", e.IndexDir, columnName)
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// Read number of unique values
	var numValues uint32
	binary.Read(file, binary.BigEndian, &numValues)

	// Binary search through the index
	left, right := uint32(0), numValues-1

	for left <= right {
		mid := (left + right) / 2

		// Seek to the middle entry
		offset := 4 + (mid * 16) // Approximate offset calculation
		file.Seek(int64(offset), 0)

		// Read key length and key
		var keyLen uint32
		binary.Read(file, binary.BigEndian, &keyLen)
		keyBytes := make([]byte, keyLen)
		file.Read(keyBytes)
		key := string(keyBytes)

		// Read offset and count
		var dataOffset, count uint32
		binary.Read(file, binary.BigEndian, &dataOffset)
		binary.Read(file, binary.BigEndian, &count)

		cmp := strings.Compare(searchValue, key)
		if cmp == 0 {
			// Found the key, read line numbers
			file.Seek(int64(dataOffset), 0)
			var actualCount uint32
			binary.Read(file, binary.BigEndian, &actualCount)

			lineNums := make([]uint32, actualCount)
			for i := uint32(0); i < actualCount; i++ {
				binary.Read(file, binary.BigEndian, &lineNums[i])
			}
			return lineNums, nil
		} else if cmp < 0 {
			right = mid - 1
		} else {
			left = mid + 1
		}
	}

	return []uint32{}, nil // Not found
}

func (e *BinarySearchEngine) GetName() string {
	return "BinarySearch"
}

func (e *BinarySearchEngine) GetDescription() string {
	return "Binary search on sorted index - O(log n) lookup"
}

// 2. Hash-based Query Engine
type HashQueryEngine struct {
	IndexDir string
}

func (e *HashQueryEngine) Search(columnName, searchValue string) ([]uint32, error) {
	filename := fmt.Sprintf("%s/%s_hash.hidx", e.IndexDir, columnName)
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// Read bucket size
	var bucketSize uint32
	binary.Read(file, binary.BigEndian, &bucketSize)

	// Calculate hash and bucket
	hash := hashString(searchValue)
	bucketIndex := hash % bucketSize

	// Read bucket table entry
	bucketTableOffset := 4 + (bucketIndex * 8)
	file.Seek(int64(bucketTableOffset), 0)

	var bucketOffset, bucketCount uint32
	binary.Read(file, binary.BigEndian, &bucketOffset)
	binary.Read(file, binary.BigEndian, &bucketCount)

	// Search through bucket entries
	file.Seek(int64(bucketOffset), 0)
	for i := uint32(0); i < bucketCount; i++ {
		var entryHash, dataOffset, dataLength uint32
		binary.Read(file, binary.BigEndian, &entryHash)
		binary.Read(file, binary.BigEndian, &dataOffset)
		binary.Read(file, binary.BigEndian, &dataLength)

		if entryHash == hash {
			// Found potential match, verify the actual value
			currentPos, _ := file.Seek(0, 1) // Get current position
			file.Seek(int64(dataOffset), 0)

			var valueLen uint32
			binary.Read(file, binary.BigEndian, &valueLen)
			valueBytes := make([]byte, valueLen)
			file.Read(valueBytes)

			if string(valueBytes) == searchValue {
				// Found exact match, read line numbers
				var lineCount uint32
				binary.Read(file, binary.BigEndian, &lineCount)

				lineNums := make([]uint32, lineCount)
				for j := uint32(0); j < lineCount; j++ {
					binary.Read(file, binary.BigEndian, &lineNums[j])
				}
				return lineNums, nil
			}

			file.Seek(currentPos, 0) // Restore position
		}
	}

	return []uint32{}, nil // Not found
}

func (e *HashQueryEngine) GetName() string {
	return "HashLookup"
}

func (e *HashQueryEngine) GetDescription() string {
	return "Hash-based lookup - O(1) average case"
}

// 3. Compressed Index Query Engine
type CompressedQueryEngine struct {
	IndexDir string
}

func (e *CompressedQueryEngine) Search(columnName, searchValue string) ([]uint32, error) {
	filename := fmt.Sprintf("%s/%s_compressed.cidx", e.IndexDir, columnName)
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// Open gzip reader
	gzReader, err := gzip.NewReader(file)
	if err != nil {
		return nil, err
	}
	defer gzReader.Close()

	// Read dictionary
	var dictSize uint32
	binary.Read(gzReader, binary.BigEndian, &dictSize)

	dictionary := make(map[string]uint32)
	reverseDictionary := make(map[uint32]string)

	for i := uint32(0); i < dictSize; i++ {
		var valueLen uint32
		binary.Read(gzReader, binary.BigEndian, &valueLen)
		valueBytes := make([]byte, valueLen)
		gzReader.Read(valueBytes)

		var index uint32
		binary.Read(gzReader, binary.BigEndian, &index)

		value := string(valueBytes)
		dictionary[value] = index
		reverseDictionary[index] = value
	}

	// Check if search value exists in dictionary
	targetIndex, exists := dictionary[searchValue]
	if !exists {
		return []uint32{}, nil
	}

	// Read compressed data and find matches
	var dataSize uint32
	binary.Read(gzReader, binary.BigEndian, &dataSize)

	var lineNums []uint32
	for i := uint32(0); i < dataSize; i++ {
		var valueIndex, lineNum uint32
		binary.Read(gzReader, binary.BigEndian, &valueIndex)
		binary.Read(gzReader, binary.BigEndian, &lineNum)

		if valueIndex == targetIndex {
			lineNums = append(lineNums, lineNum)
		}
	}

	return lineNums, nil
}

func (e *CompressedQueryEngine) GetName() string {
	return "Compressed"
}

func (e *CompressedQueryEngine) GetDescription() string {
	return "Dictionary-compressed index with gzip"
}

// 4. JSON Query Engine
type JSONQueryEngine struct {
	IndexDir string
}

func (e *JSONQueryEngine) Search(columnName, searchValue string) ([]uint32, error) {
	filename := fmt.Sprintf("%s/%s_json.jidx", e.IndexDir, columnName)
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var valueMap map[string][]uint32
	decoder := json.NewDecoder(file)
	err = decoder.Decode(&valueMap)
	if err != nil {
		return nil, err
	}

	if lineNums, exists := valueMap[searchValue]; exists {
		return lineNums, nil
	}

	return []uint32{}, nil
}

func (e *JSONQueryEngine) GetName() string {
	return "JSON"
}

func (e *JSONQueryEngine) GetDescription() string {
	return "JSON-based index lookup"
}

// 5. Columnar Query Engine
type ColumnarQueryEngine struct {
	IndexDir string
}

func (e *ColumnarQueryEngine) Search(columnName, searchValue string) ([]uint32, error) {
	filename := fmt.Sprintf("%s/%s_columnar.colidx", e.IndexDir, columnName)
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// Read header
	var recordCount uint32
	binary.Read(file, binary.BigEndian, &recordCount)

	// Read all values first
	values := make([]string, recordCount)
	for i := uint32(0); i < recordCount; i++ {
		var valueLen uint32
		binary.Read(file, binary.BigEndian, &valueLen)
		valueBytes := make([]byte, valueLen)
		file.Read(valueBytes)
		values[i] = string(valueBytes)
	}

	// Read all line numbers
	lineNums := make([]uint32, recordCount)
	for i := uint32(0); i < recordCount; i++ {
		binary.Read(file, binary.BigEndian, &lineNums[i])
	}

	// Find matches
	var results []uint32
	for i := uint32(0); i < recordCount; i++ {
		if values[i] == searchValue {
			results = append(results, lineNums[i])
		}
	}

	return results, nil
}

func (e *ColumnarQueryEngine) GetName() string {
	return "Columnar"
}

func (e *ColumnarQueryEngine) GetDescription() string {
	return "Columnar storage scan"
}

// 6. LZ4 Query Engine
type LZ4QueryEngine struct {
	IndexDir string
}

func (e *LZ4QueryEngine) Search(columnName, searchValue string) ([]uint32, error) {
	filename := fmt.Sprintf("%s/%s_lz4.lz4idx", e.IndexDir, columnName)
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// Open gzip reader (using gzip instead of LZ4 for now)
	gzReader, err := gzip.NewReader(file)
	if err != nil {
		return nil, err
	}
	defer gzReader.Close()

	var valueMap map[string][]uint32
	decoder := json.NewDecoder(gzReader)
	err = decoder.Decode(&valueMap)
	if err != nil {
		return nil, err
	}

	if lineNums, exists := valueMap[searchValue]; exists {
		return lineNums, nil
	}

	return []uint32{}, nil
}

func (e *LZ4QueryEngine) GetName() string {
	return "LZ4"
}

func (e *LZ4QueryEngine) GetDescription() string {
	return "LZ4-compressed index lookup"
}

// 7. Linear Scan Engine (baseline for comparison)
type LinearScanEngine struct {
	DataFile string
}

func (e *LinearScanEngine) Search(columnName, searchValue string) ([]uint32, error) {
	file, err := os.Open(e.DataFile)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// This is a simplified linear scan - in practice you'd need to parse CSV
	// For now, return empty result as this is just for benchmarking structure
	return []uint32{}, nil
}

func (e *LinearScanEngine) GetName() string {
	return "LinearScan"
}

func (e *LinearScanEngine) GetDescription() string {
	return "Linear scan through original data - O(n) baseline"
}

// Benchmark function
func BenchmarkQueryEngines(indexDir string, queries []QueryTest) {
	engines := []QueryEngine{
		&BinarySearchEngine{IndexDir: indexDir},
		&HashQueryEngine{IndexDir: indexDir},
		&CompressedQueryEngine{IndexDir: indexDir},
		&JSONQueryEngine{IndexDir: indexDir},
		&ColumnarQueryEngine{IndexDir: indexDir},
		&LZ4QueryEngine{IndexDir: indexDir},
	}

	fmt.Printf("%-15s %-30s %-10s %-10s %-10s\n",
		"Engine", "Description", "Avg Time", "Min Time", "Max Time")
	fmt.Println(strings.Repeat("-", 80))

	for _, engine := range engines {
		var totalTime, minTime, maxTime time.Duration
		minTime = time.Hour // Initialize to large value
		successCount := 0

		for _, query := range queries {
			start := time.Now()
			results, err := engine.Search(query.Column, query.Value)
			duration := time.Since(start)

			if err == nil {
				totalTime += duration
				if duration < minTime {
					minTime = duration
				}
				if duration > maxTime {
					maxTime = duration
				}
				successCount++

				fmt.Printf("  %s: Found %d results in %v\n",
					query.Column, len(results), duration)
			} else {
				fmt.Printf("  %s: Error - %v\n", query.Column, err)
			}
		}

		avgTime := time.Duration(0)
		if successCount > 0 {
			avgTime = totalTime / time.Duration(successCount)
		}

		fmt.Printf("%-15s %-30s %-10v %-10v %-10v\n",
			engine.GetName(), engine.GetDescription(),
			avgTime, minTime, maxTime)
	}
}

type QueryTest struct {
	Column string
	Value  string
}
