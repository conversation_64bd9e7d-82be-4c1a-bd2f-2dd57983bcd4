// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.2
// source: map_data.proto

package protob

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MapData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data map[int32]string `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *MapData) Reset() {
	*x = MapData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_map_data_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MapData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MapData) ProtoMessage() {}

func (x *MapData) ProtoReflect() protoreflect.Message {
	mi := &file_map_data_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MapData.ProtoReflect.Descriptor instead.
func (*MapData) Descriptor() ([]byte, []int) {
	return file_map_data_proto_rawDescGZIP(), []int{0}
}

func (x *MapData) GetData() map[int32]string {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_map_data_proto protoreflect.FileDescriptor

var file_map_data_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x6d, 0x61, 0x70, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x6a, 0x0a, 0x07, 0x4d, 0x61, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x26, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x4d, 0x61, 0x70, 0x44,
	0x61, 0x74, 0x61, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x1a, 0x37, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x0a, 0x5a, 0x08,
	0x2e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_map_data_proto_rawDescOnce sync.Once
	file_map_data_proto_rawDescData = file_map_data_proto_rawDesc
)

func file_map_data_proto_rawDescGZIP() []byte {
	file_map_data_proto_rawDescOnce.Do(func() {
		file_map_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_map_data_proto_rawDescData)
	})
	return file_map_data_proto_rawDescData
}

var file_map_data_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_map_data_proto_goTypes = []interface{}{
	(*MapData)(nil), // 0: MapData
	nil,             // 1: MapData.DataEntry
}
var file_map_data_proto_depIdxs = []int32{
	1, // 0: MapData.data:type_name -> MapData.DataEntry
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_map_data_proto_init() }
func file_map_data_proto_init() {
	if File_map_data_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_map_data_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MapData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_map_data_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_map_data_proto_goTypes,
		DependencyIndexes: file_map_data_proto_depIdxs,
		MessageInfos:      file_map_data_proto_msgTypes,
	}.Build()
	File_map_data_proto = out.File
	file_map_data_proto_rawDesc = nil
	file_map_data_proto_goTypes = nil
	file_map_data_proto_depIdxs = nil
}
